import { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Avatar,
  Chip,
  InputAdornment,
  useTheme,
  Grid,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';
import { useRouter } from 'src/routes/hooks';
import { useTemplates, Template } from 'src/hooks/use-templates';

// ----------------------------------------------------------------------

// Filter options
const STATUS_FILTERS = ['All', 'Active (6)', 'Inactive'];
const CATEGORY_FILTERS = ['All', 'Social Media', 'Marketing', 'Sales'];

// Agent Card Component
const AgentCard = ({ agent }: { agent: Template }) => {
  const theme = useTheme();
  const router = useRouter();

  const handleUseTemplate = () => {
    // Navigate to the conversation chat page
    router.push('/dashboard/agents/chat');
  };

  return (
    <Card
      sx={{
        p: 3,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        background: (theme) =>
          `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
        backdropFilter: 'blur(10px)',
        borderRadius: 3,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: (theme) =>
            `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.error.main} 100%)`,
          opacity: 0,
          transition: 'opacity 0.3s ease',
        },
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
          borderColor: 'primary.main',
          '&::before': {
            opacity: 1,
          },
        },
      }}
    >
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              {agent.name}
            </Typography>
            <Chip
              label={agent.type}
              size="small"
              sx={{
                bgcolor: (theme) => theme.palette.primary.light,
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.025em',
                border: (theme) => `1px solid ${theme.palette.primary.main}40`,
                borderRadius: '6px',
                height: '24px',
                '& .MuiChip-label': {
                  px: 1.5,
                },
              }}
            />
          </Box>
          <IconButton
            size="small"
            sx={{
              color: 'text.secondary',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: (theme) => theme.palette.primary.main + '10',
                color: 'primary.main',
                transform: 'scale(1.05)',
              },
            }}
          >
            <Icon icon="eva:more-horizontal-fill" width={18} height={18} />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.4 }}>
          {agent.description}
        </Typography>

        <Box display="flex" alignItems="center" gap={1} mb={3}>
          <Avatar
            sx={{
              width: 44,
              height: 44,
              bgcolor: 'action.hover',
              border: '1px solid',
              borderColor: 'divider',
              transition: 'all 0.2s ease',
              '&:hover': {
                transform: 'scale(1.1)',
                bgcolor: 'action.selected',
                borderColor: 'primary.main',
              },
            }}
          >
            <Icon icon={agent.icon} width={35} height={35} />
          </Avatar>
        </Box>

        <AppButton
          variant="outlined"
          color="primary"
          fullWidth
          onClick={handleUseTemplate}
          label=" Use Template"
        />
      </CardContent>
    </Card>
  );
};

export function AgentsView() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAgents, setFilteredAgents] = useState<AgentTemplate[]>(AGENT_TEMPLATES);
  const [selectedStatusTab, setSelectedStatusTab] = useState(0);
  const [selectedCategoryTab, setSelectedCategoryTab] = useState(0);

  // Filter agents based on search, status, and category
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    filterAgents(query, selectedStatusTab, selectedCategoryTab);
  };

  const handleStatusTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedStatusTab(newValue);
    filterAgents(searchQuery, newValue, selectedCategoryTab);
  };

  const handleCategoryTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedCategoryTab(newValue);
    filterAgents(searchQuery, selectedStatusTab, newValue);
  };

  const filterAgents = (query: string, statusTabIndex: number, categoryTabIndex: number) => {
    let filtered = AGENT_TEMPLATES;

    // Filter by search query
    if (query) {
      filtered = filtered.filter(
        (agent) =>
          agent.name.toLowerCase().includes(query.toLowerCase()) ||
          agent.description.toLowerCase().includes(query.toLowerCase()) ||
          agent.category.toLowerCase().includes(query.toLowerCase())
      );
    }

    // Filter by status
    if (statusTabIndex !== 0) {
      // 0 = All
      if (statusTabIndex === 1) {
        // Active
        filtered = filtered.filter((agent) => agent.status === 'active');
      } else if (statusTabIndex === 2) {
        // Inactive
        filtered = filtered.filter((agent) => agent.status === 'inactive');
      }
    }

    // Filter by category
    if (categoryTabIndex !== 0) {
      // 0 = All
      const categoryName = CATEGORY_FILTERS[categoryTabIndex];
      filtered = filtered.filter((agent) => agent.category === categoryName);
    }

    setFilteredAgents(filtered);
  };

  return (
    <Box
      sx={{
        width: '100%',
        minHeight: '100vh',
        bgcolor: 'background.default',
        p: { xs: 2, sm: 3, md: 4 },
        maxWidth: '1400px',
        mx: 'auto',
      }}
    >
      {/* Header Section */}
      <Box sx={{ mb: 5 }}>
        <Typography
          variant="h3"
          fontWeight={700}
          color="text.primary"
          sx={{
            mb: 1,
            fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
          }}
        >
          Agents
        </Typography>
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            mb: 3,
            fontSize: '1.1rem',
            opacity: 0.8,
          }}
        >
          Enable advanced workflows with applications
        </Typography>
      </Box>

      {/* Agents Templates Section */}
      <Box sx={{ mb: 6 }}>
        <Typography
          variant="h4"
          fontWeight={700}
          color="text.primary"
          sx={{
            mb: 4,
            fontSize: { xs: '1.5rem', sm: '1.75rem', md: '2rem' },
          }}
        >
          Agents&apos; Templates
        </Typography>

        {/* Filter Section */}
        <Box sx={{ mb: 6, width: '100%' }}>
          {/* Status Filter Tabs - Above Search */}
          <Box sx={{ mb: 3 }}>
            <Box
              sx={{
                display: 'inline-flex',
                bgcolor: 'rgba(24, 0, 72, 0.08)',
                height: 36,
                borderRadius: '10px',
              }}
            >
              <Tabs
                value={selectedStatusTab}
                onChange={handleStatusTabChange}
                variant="scrollable"
                scrollButtons="auto"
                sx={{
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 500,
                    minHeight: 36,
                    height: 36,

                    px: 3,

                    py: 1,
                    minWidth: 'auto',
                    border: '1px solid ',
                    borderColor: 'transparent',
                    bgcolor: 'transparent', 
                    color: 'text.secondary',
                    transition: 'all 0.2s ease',
                    '&.Mui-selected': {
                      color: 'inherit',
                      borderColor: 'rgba(24, 0, 72, 0.08)',
                      fontWeight: 600,
                      borderRadius: '10px',
                    },
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  },
                  '& .MuiTabs-indicator': {
                    display: 'none',
                  },
                  '& .MuiTabs-flexContainer': {
                    gap: 0,
                  },
                }}
              >
                {STATUS_FILTERS.map((filter, index) => (
                  <Tab key={index} label={filter} />
                ))}
              </Tabs>
            </Box>
          </Box>

          {/* Search Bar */}
          <Box sx={{ mb: 3, width: '100%' }}>
            <TextField
              fullWidth
              placeholder="Search"
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Iconify icon="eva:search-fill" width={20} sx={{ color: 'inherit' }} />
                  </InputAdornment>
                ),
              }}
              sx={{
                width: '100%',
                '& .MuiOutlinedInput-root': {
borderColor:'transparent',
                  borderRadius: 1,
                  bgcolor: 'rgba(24, 0, 72, 0.08)',
                  height: 48,
                  fontSize: '0.95rem',
                  '&:hover': {
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                    },
                  },
                  '&.Mui-focused': {
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'primary.main',
                      borderWidth: 2,
                    },
                  },
                },
              }}
            />
          </Box>

          {/* Category Filter Tabs - Below Search */}
          <Box>
            <Tabs
              value={selectedCategoryTab}
              onChange={handleCategoryTabChange}
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                '& .MuiTab-root': {
                  textTransform: 'none',
                  fontWeight: 500,
                  minHeight: 36,
                  height: 36,
                  px: 3,
                  py: 1,
                  minWidth: 'auto',
                  borderRadius: '20px',
                  border: '1px solid',
                  borderColor: 'divider',
                  bgcolor: 'rgba(24, 0, 72, 0.08)',
                  color: 'text.secondary',
                  transition: 'all 0.2s ease',
                  '&.Mui-selected': {
                    color: 'inherit',
                    bgcolor: 'rgba(163, 139, 233, 0.33)',
                    borderColor: 'primary.main',
                    fontWeight: 600,
                  },
                  '&:hover': {
                    bgcolor: 'action.hover',
                  },
                },
                '& .MuiTabs-indicator': {
                  display: 'none',
                },
                '& .MuiTabs-flexContainer': {
                  gap: 1,
                },
              }}
            >
              {CATEGORY_FILTERS.map((filter, index) => (
                <Tab key={index} label={filter} />
              ))}
            </Tabs>
          </Box>
        </Box>

        {/* Agents Grid */}
        <Grid container spacing={{ xs: 2, sm: 3, md: 4 }}>
          {filteredAgents.map((agent) => (
            <Grid item xs={12} sm={6} lg={4} key={agent.id}>
              <AgentCard agent={agent} />
            </Grid>
          ))}
        </Grid>

        {/* No Results */}
        {filteredAgents.length === 0 && (
          <Box
            sx={{
              textAlign: 'center',
              py: 12,
              px: 4,
            }}
          >
            <Box
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 3,
                borderRadius: '50%',
                bgcolor: 'action.hover',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}
            >
              <Iconify
                icon="eva:search-outline"
                width={48}
                height={48}
                sx={{ color: 'text.disabled' }}
              />
            </Box>
            <Typography variant="h5" color="text.primary" sx={{ mb: 2, fontWeight: 600 }}>
              No agents found
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
              Try adjusting your search or filter criteria to find the agents you&apos;re looking
              for
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
}

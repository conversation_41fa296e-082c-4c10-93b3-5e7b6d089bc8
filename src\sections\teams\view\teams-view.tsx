import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  InputAdornment,
  Chip,
  Avatar,
  IconButton,
  Grid,
} from '@mui/material';
import { Icon } from '@iconify/react';
import { useTheme } from '@mui/material/styles';
import { useRouter } from 'src/routes/hooks';
import { AppButton } from 'src/components/common';

const TeamsView = () => {
  const theme = useTheme();
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');

  const categories = ['All', 'Social Media', 'Marketing', 'Sales'];

  const recentlyUsedTeam = [
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Pinterest', icon: 'logos:pinterest', color: '#bd081c' },
      ],
    },
  ];
  const teamTemplates = [
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Figma', icon: 'logos:figma', color: '#f24e1e' },
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
        { name: 'Notion', icon: 'logos:notion-icon', color: '#000000' },
        { name: 'GitHub', icon: 'logos:github-icon', color: '#181717' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Pinterest', icon: 'logos:pinterest', color: '#bd081c' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Atlassian', icon: 'logos:atlassian', color: '#0052cc' },
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Behance', icon: 'logos:behance', color: '#1769ff' },
        { name: 'Pinterest', icon: 'logos:pinterest', color: '#bd081c' },
        { name: 'Mastercard', icon: 'logos:mastercard', color: '#eb001b' },
        { name: 'Vimeo', icon: 'logos:vimeo-icon', color: '#1ab7ea' },
        { name: 'GitHub', icon: 'logos:github-icon', color: '#181717' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
        { name: 'Notion', icon: 'logos:notion-icon', color: '#000000' },
      ],
    },
    {
      name: 'AI Team',
      type: 'Template',
      description:
        'Boost your online presence effortlessly with AI-driven automation. Schedule posts, analyze eng...',
      platforms: [
        { name: 'Telegram', icon: 'logos:telegram', color: '#0088cc' },
        { name: 'Figma', icon: 'logos:figma', color: '#f24e1e' },
        { name: 'Gmail', icon: 'logos:google-gmail', color: '#ea4335' },
        { name: 'Notion', icon: 'logos:notion-icon', color: '#000000' },
        { name: 'GitHub', icon: 'logos:github-icon', color: '#181717' },
      ],
    },
  ];

  const handleUseTemplate = () => {
    router.push('/dashboard/teams/create');
  };

  const TeamCard = ({ team }: { team: any }) => (
    <Card
      sx={{
        p: 3,
        height: '100%',
        border: '1px solid',
        borderColor: 'divider',
        background: (theme) =>
          `linear-gradient(145deg, ${theme.palette.background.paper} 0%, ${theme.palette.background.default} 100%)`,
        backdropFilter: 'blur(10px)',
        borderRadius: 3,
        position: 'relative',
        overflow: 'hidden',
        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
        cursor: 'pointer',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          height: '3px',
          background: (theme) =>
            `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 50%, ${theme.palette.error.main} 100%)`,
          opacity: 0,
          transition: 'opacity 0.3s ease',
        },
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: (theme) => `${theme.shadows[8]}, 0 0 0 1px ${theme.palette.primary.main}20`,
          borderColor: 'primary.main',
          '&::before': {
            opacity: 1,
          },
        },
      }}
    >
      <CardContent sx={{ p: 0, '&:last-child': { pb: 0 } }}>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Typography variant="h6" fontWeight={600} color="text.primary">
              {team.name}
            </Typography>
            <Chip
              label={team.type}
              size="small"
              sx={{
                bgcolor:(theme) => theme.palette.primary.light ,
                fontWeight: 600,
                fontSize: '0.75rem',
                letterSpacing: '0.025em',
                border: (theme) => `1px solid ${theme.palette.primary.main}40`,
                borderRadius: '6px',
                height: '24px',
                '& .MuiChip-label': {
                  px: 1.5,
                },
              }}
            />
          </Box>
          <IconButton
            size="small"
            sx={{
              color: 'text.secondary',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: (theme) => theme.palette.primary.main + '10',
                color: 'primary.main',
                transform: 'scale(1.05)',
              },
            }}
          >
            <Icon icon="eva:more-horizontal-fill" width={18} height={18} />
          </IconButton>
        </Box>

        <Typography variant="body2" color="text.secondary" sx={{ mb: 2, lineHeight: 1.4 }}>
          {team.description}
        </Typography>

        <Box display="flex" alignItems="center" gap={1} mb={3}>
          {team.platforms.slice(0, 5).map((platform: any, index: number) => (
            <Avatar
              key={index}
              sx={{
                width: 32,
                height: 32,
                bgcolor: 'action.hover',
                border: '1px solid',
                borderColor: 'divider',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  bgcolor: 'action.selected',
                  borderColor: 'primary.main',
                },
              }}
            >
              <Icon icon={platform.icon} width={20} height={20} />
            </Avatar>
          ))}
          {team.platforms.length > 5 && (
            <Avatar
              sx={{
                width: 32,
                height: 32,
                bgcolor: (theme) => theme.palette.primary.main + '20',
                color: 'primary.main',
                fontSize: '0.75rem',
                fontWeight: 600,
                border: '1px solid',
                borderColor: (theme) => theme.palette.primary.main + '40',
                transition: 'all 0.2s ease',
                '&:hover': {
                  transform: 'scale(1.1)',
                  bgcolor: (theme) => theme.palette.primary.main + '30',
                },
              }}
            >
              +{team.platforms.length - 5}
            </Avatar>
          )}
        </Box>

        <AppButton
          variant="outlined"
          color="primary"
          fullWidth
          onClick={handleUseTemplate}
          label=" Use Template"
        />
      </CardContent>
    </Card>
  );

  return (
    <Box
      sx={{
        p: { xs: 2, sm: 3, md: 4 },
        bgcolor: 'background.default',
        minHeight: '100vh',
        position: 'relative',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: (theme) =>
            `radial-gradient(circle at 20% 20%, ${theme.palette.primary.main}10 0%, transparent 50%), radial-gradient(circle at 80% 80%, ${theme.palette.secondary.main}10 0%, transparent 50%)`,
          pointerEvents: 'none',
        },
      }}
    >
      {/* Header */}
      <Box
        display="flex"
        justifyContent="space-between"
        alignItems="center"
        mb={5}
        sx={{ position: 'relative', zIndex: 1 }}
      >
        <Box>
          <Typography
            variant="h3"
            fontWeight={800}
            color="text.primary"
            sx={{
              mb: 1,
            }}
          >
            Teams
          </Typography>
          <Typography
            variant="body1"
            color="text.secondary"
            sx={{
              fontSize: '1.1rem',
              opacity: 0.8,
            }}
          >
            Collaborate and build amazing projects together
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<Icon icon="eva:plus-fill" width={20} height={20} />}
          sx={{
            bgcolor: 'primary.main',
            color: 'primary.contrastText',
            px: 3,
            py: 1.5,
            borderRadius: 2,
            fontWeight: 600,
            fontSize: '0.95rem',
            textTransform: 'none',
            boxShadow: (theme) => `0 8px 32px ${theme.palette.primary.main}30`,
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: (theme) => `0 12px 40px ${theme.palette.primary.main}40`,
              bgcolor: 'primary.dark',
            },
          }}
        >
          Create Team
        </Button>
      </Box>

      {/* Recently Used Section */}
      <Box mb={6} sx={{ position: 'relative', zIndex: 1 }}>
        <Box mb={3}>
          <Typography
            variant="h5"
            fontWeight={700}
            color="text.primary"
            sx={{
              mb: 0.5,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Icon
              icon="eva:clock-outline"
              width={24}
              height={24}
              color={theme.palette.primary.main}
            />
            Recently Used
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ opacity: 0.8 }}>
            Your most recent team templates and projects
          </Typography>
        </Box>
        <Grid container spacing={3}>
          {recentlyUsedTeam.map((template, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <TeamCard team={template} />
            </Grid>
          ))}
        </Grid>
      </Box>

      {/* Teams Templates Section */}
      <Box sx={{ width: '100%', position: 'relative', zIndex: 1 }}>
        <Box mb={3}>
          <Typography
            variant="h5"
            fontWeight={700}
            color="text.primary"
            sx={{
              mb: 0.5,
              display: 'flex',
              alignItems: 'center',
              gap: 1,
            }}
          >
            <Icon
              icon="eva:grid-outline"
              width={24}
              height={24}
              color={theme.palette.primary.main}
            />
            Teams Templates
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ opacity: 0.8 }}>
            Discover and use pre-built team templates for your projects
          </Typography>
        </Box>

        {/* Search Bar */}
        <TextField
          placeholder="Search teams and templates..."
          fullWidth
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Icon
                  icon="eva:search-fill"
                  color={theme.palette.text.secondary}
                  width={20}
                  height={20}
                />
              </InputAdornment>
            ),
          }}
          sx={{
            mb: 3,
            '& .MuiOutlinedInput-root': {
              bgcolor: 'background.paper',
              backdropFilter: 'blur(8px)',
              color: 'text.primary',
              borderRadius: 2,
              transition: 'all 0.2s ease-in-out',
              '& fieldset': {
                borderColor: 'divider',
                borderWidth: '1.5px',
              },
              '&:hover': {
                bgcolor: 'background.paper',
                '& fieldset': {
                  borderColor: (theme) => theme.palette.primary.main + '60',
                },
              },
              '&.Mui-focused': {
                bgcolor: 'background.paper',
                '& fieldset': {
                  borderColor: 'primary.main',
                  borderWidth: '2px',
                },
              },
            },
            '& .MuiInputBase-input': {
              color: 'text.primary',
              fontSize: '0.95rem',
              padding: '14px 16px',
              '&::placeholder': {
                color: 'text.secondary',
                opacity: 0.8,
              },
            },
          }}
        />

        {/* Category Filters */}
        <Box display="flex" gap={1} mb={3}>
          {categories.map((category) => (
            <Chip
              key={category}
              label={category}
              onClick={() => setSelectedCategory(category)}
              sx={{
                bgcolor:
                  selectedCategory === category
                    ? (theme) => theme.palette.grey[100] 
                    : theme.palette.secondary.main,
                color: selectedCategory === category ? theme.palette.secondary.main : theme.palette.secondary.lighter,
                border: '1px solid',
                borderColor: selectedCategory === category ? 'primary.main' : 'divider',
                fontWeight: selectedCategory === category ? 600 : 500,
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  bgcolor:
                    selectedCategory === category
                      ? (theme) => theme.palette.primary.main + '30'
                      : 'action.selected',
                  borderColor:
                    selectedCategory === category
                      ? 'primary.dark'
                      : (theme) => theme.palette.primary.main + '40',
                  transform: 'translateY(-1px)',
                  boxShadow: (theme) => theme.shadows[2],
                },
              }}
            />
          ))}
        </Box>

        {/* Templates Grid */}
        <Grid container spacing={3}>
          {teamTemplates.map((template, index) => (
            <Grid item xs={12} sm={6} md={4} key={index}>
              <TeamCard team={template} />
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default TeamsView;

import { useState } from 'react';
import { Box, Stack, Typography, TextField, InputAdornment, IconButton } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { Iconify } from 'src/components/iconify';
import { AppButton } from 'src/components/common';

// ----------------------------------------------------------------------

export function PasswordSettings() {
  const { t } = useTranslation();
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  return (
    <Stack spacing={3}>
      <Typography variant="h3">{t('Password')}</Typography>

      <Stack spacing={2}>
        {/* Current Password Field */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" component="div" sx={{ mb: 1 }}>
            {t('Current Password')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showCurrentPassword ? 'text' : 'password'}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton edge="start">
                    <Iconify icon="solar:key-outline" width={24} />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    <Iconify
                      icon={showCurrentPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                      width={24}
                      sx={{ color: 'primary.main' }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* New Password Field */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" component="div" sx={{ mb: 1 }}>
            {t('New Password')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            helperText="Minimum 8 characters"
            type={showNewPassword ? 'text' : 'password'}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton edge="start">
                    <Iconify icon="solar:key-outline" width={24} />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton edge="end" onClick={() => setShowNewPassword(!showNewPassword)}>
                    <Iconify
                      icon={showNewPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                      width={24}
                      sx={{ color: 'primary.main' }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {/* Confirm New Password Field */}
        <Box sx={{ mb: 1 }}>
          <Typography variant="body2" component="div" sx={{ mb: 1 }}>
            {t('Confirm New Password')}
          </Typography>
          <TextField
            fullWidth
            size="small"
            type={showConfirmPassword ? 'text' : 'password'}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <IconButton edge="start">
                    <Iconify icon="solar:key-outline" width={24} />
                  </IconButton>
                </InputAdornment>
              ),
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    edge="end"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    <Iconify
                      icon={showConfirmPassword ? 'mdi:eye-off-outline' : 'mdi:eye-outline'}
                      width={24}
                      sx={{ color: 'primary.main' }}
                    />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Box>
      </Stack>

      <Stack direction="row" justifyContent="end" sx={{ pt: 3 }}>
         <AppButton
          variant="contained"
          sx={{
            width: '12%',
            whiteSpace:'nowrap',
            borderRadius: 1,
            alignSelf: 'flex-end',
            mt: 3,
            bgcolor: 'primary.main',
          }}
          label={t('Reset Password')}
        />
      </Stack>
    </Stack>
  );
}

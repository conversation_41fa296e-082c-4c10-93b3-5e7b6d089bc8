import { useTemplatesApi, Template } from 'src/services/api/templates-api';

// Custom hook to manage templates data
export const useTemplates = () => {
  const { useGetTemplates } = useTemplatesApi();

  // Fetch all templates
  const {
    data: templates = [],
    isLoading,
    error,
    refetch,
  } = useGetTemplates();

  return {
    templates,
    isLoading,
    error,
    refetch,
  };
};

export type { Template };
